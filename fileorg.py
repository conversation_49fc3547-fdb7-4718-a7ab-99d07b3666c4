import os
import shutil
from datetime import datetime
from pathlib import Path

def organize_files(target_base_dir):
    # 获取当前日期
    today = datetime.now()
    year = str(today.year)
    month = str(today.month).zfill(2)
    day = str(today.day).zfill(2)
    
    # 构建目标路径
    year_dir = os.path.join(target_base_dir, year)
    month_dir = os.path.join(year_dir, f"{year}-{month}")
    day_dir = os.path.join(month_dir, f"{year}{month}{day}")
    
    # 获取桌面路径
    desktop_path = str(Path.home() / "Desktop")
    
    # 定义要移动的文件类型
    file_extensions = {
        'documents': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.xls', '.xlsx', '.ppt', '.pptx'],
        'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'],
        'videos': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'],
        'audio': ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'],
        'compressed': ['.zip', '.rar', '.tar', '.gz', '.bz2', '.7z', '.iso', '.dmg', '.pkg', '.deb', '.rpm', '.exe', '.msi', '.app']
    }
    
    # 检查是否有需要移动的文件和文件夹
    files_to_move = []
    folders_to_move = []
    for filename in os.listdir(desktop_path):
        file_path = os.path.join(desktop_path, filename)
        if os.path.isdir(file_path):
            # 排除 ybtan_nas 文件夹
            if 'ybtan_nas' not in filename:
                folders_to_move.append((filename, file_path))
            continue
        file_ext = os.path.splitext(filename)[1].lower()
        for category in file_extensions.values():
            if file_ext in category:
                files_to_move.append((filename, file_path))
                break
    
    if not files_to_move and not folders_to_move:
        print("没有需要移动的文件或文件夹")
        return
        
    # 创建目录
    os.makedirs(year_dir, exist_ok=True)
    os.makedirs(month_dir, exist_ok=True)
    os.makedirs(day_dir, exist_ok=True)
    
    # 移动文件
    for filename, file_path in files_to_move:
        try:
            target_path = os.path.join(day_dir, filename)
            if os.path.exists(target_path):
                name, ext = os.path.splitext(filename)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_filename = f"{name}_{timestamp}{ext}"
                target_path = os.path.join(day_dir, new_filename)
                print(f"文件 {filename} 已存在，将重命名为: {new_filename}")
            shutil.move(file_path, target_path)
            print(f"已移动文件: {filename}")
        except Exception as e:
            print(f"移动文件 {filename} 时出错: {str(e)}")
    
    # 移动文件夹
    for foldername, folder_path in folders_to_move:
        try:
            target_path = os.path.join(day_dir, foldername)
            if os.path.exists(target_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_foldername = f"{foldername}_{timestamp}"
                target_path = os.path.join(day_dir, new_foldername)
                print(f"文件夹 {foldername} 已存在，将重命名为: {new_foldername}")
            shutil.move(folder_path, target_path)
            print(f"已移动文件夹: {foldername}")
        except Exception as e:
            print(f"移动文件夹 {foldername} 时出错: {str(e)}")

if __name__ == "__main__":
    target_directory = '/Volumes/ybtan_nas/资料'
    
    if not os.path.exists(target_directory):
        print(f"目录 {target_directory} 不存在！")
    else:
        organize_files(target_directory)
        print("文件整理完成！")