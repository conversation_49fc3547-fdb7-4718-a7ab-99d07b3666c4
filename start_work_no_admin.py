#!/usr/bin/env python3
"""
无需管理员权限的工作环境启动脚本
跳过需要管理员权限的 NoMachine 服务控制
"""

import sys
import os
import logging
from datetime import datetime
from display_utils import control_remote_display, startup_software, restore_main_display

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('start_work_no_admin.log'),
        logging.StreamHandler()
    ]
)

def enable_remote_desktop_no_admin():
    """启用远程桌面（跳过管理员权限操作）"""
    logging.info("开始执行工作环境启动脚本（无管理员权限版本）")
    
    success_count = 0
    total_operations = 3  # 跳过 NoMachine 服务控制
    
    try:
        # 1. 禁用远程显示器
        logging.info("步骤 1/3: 禁用远程显示器")
        try:
            control_remote_display(enable=False)
            success_count += 1
        except Exception as e:
            logging.error(f"禁用远程显示器失败: {e}")

        # 2. 恢复主显示屏
        logging.info("步骤 2/3: 恢复主显示屏设置")
        try:
            restore_main_display()
            success_count += 1
        except Exception as e:
            logging.error(f"恢复主显示屏失败: {e}")
        
        # 跳过 NoMachine 服务控制
        logging.info("跳过 NoMachine 服务控制（避免管理员权限要求）")

        # 3. 启动软件
        logging.info("步骤 3/3: 启动应用程序")
        try:
            startup_software()
            success_count += 1
        except Exception as e:
            logging.error(f"启动软件失败: {e}")
            
        # 总结
        logging.info(f"脚本执行完成: {success_count}/{total_operations} 个操作成功")
        
        if success_count == total_operations:
            print("🎉 所有操作都成功完成!")
            print("ℹ️ 注意: NoMachine 服务控制已跳过（避免权限问题）")
        elif success_count > 0:
            print(f"⚠️ 部分操作完成 ({success_count}/{total_operations})")
        else:
            print("❌ 所有操作都失败了")
            sys.exit(1)
            
    except Exception as e:
        logging.error(f"脚本执行过程中发生未预期的错误: {e}")
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    enable_remote_desktop_no_admin()
