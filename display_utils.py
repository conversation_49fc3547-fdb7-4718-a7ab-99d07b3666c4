#!/usr/bin/env python3
import subprocess
import sys
import time
import os
def control_remote_display(enable=True):
    """控制远程显示器的启用和禁用"""
    try:
        if enable:
            # 启用远程显示器
            subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=remote', '-connected=on'])
            time.sleep(2)  # 给足够的时间让设置生效
            subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=remote', '-hidpi=on'])
            print("✅ betterdisplay remote 屏幕已启用")
        else:
            # 禁用远程显示器
            subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=remote', '-connected=off'])
            print("✅ betterdisplay remote 屏幕已禁用")
    except subprocess.CalledProcessError as e:
        print(f"❌ 远程显示器控制失败: {e}")

def restore_main_display():
    """恢复主显示屏设置"""
    # 启用 screen 显示器
    subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=screen', '-connected=on'])
    time.sleep(2)  # 给足够的时间让设置生效

    # 设置 HiDPI
    subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=screen', '-hidpi=on'])
    time.sleep(2)  # 给足够的时间让设置生效

    # 设置旋转角度为 0
    subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=screen', '-rotation=0'])
    time.sleep(2)  # 给足够的时间让设置生效

    # 设置 screen 为主显示屏
    subprocess.run(['/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay', 'set', '-name=screen', '-main=on'])

def control_nomachine_service(start=True):
    """控制 NoMachine 服务的启动和停止"""
    try:
        if start:
            # 启动 nomachine 服务
            subprocess.run(['sudo','/Applications/NoMachine.app/Contents/Frameworks/bin/nxserver.app/Contents/MacOS/nxserver', '--startup'])
            print("✅ NoMachine 服务已启动")
        else:
            # 停止 nomachine 服务
            subprocess.run(['sudo','/Applications/NoMachine.app/Contents/Frameworks/bin/nxserver.app/Contents/MacOS/nxserver', '--shutdown'])
            print("✅ NoMachine 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ NoMachine 服务控制失败: {e}")
        sys.exit(1)

def startup_software():
    """启动软件"""
    apps = [
        '/Volumes/ybtan_nas/Applications/DingTalk.app'
    ]

    for app in apps:
        try:
            # 使用 -j 参数强制在后台启动应用
            subprocess.Popen(['open', '-j', app])
            print(f"✅ 正在启动: {os.path.basename(app)}")
            time.sleep(2)  # 给每个应用一些启动时间
        except Exception as e:
            print(f"❌ 启动 {os.path.basename(app)} 失败: {str(e)}")