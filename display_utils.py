#!/usr/bin/env python3
import subprocess
import sys
import time
import os
def control_remote_display(enable=True):
    """控制远程显示器的启用和禁用"""
    try:
        # 检查 BetterDisplay 是否存在
        betterdisplay_path = '/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay'
        if not os.path.exists(betterdisplay_path):
            print("❌ BetterDisplay 应用未找到")
            return

        if enable:
            # 启用远程显示器
            result = subprocess.run([betterdisplay_path, 'set', '-name=remote', '-connected=on'],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️ 启用远程显示器失败: {result.stderr}")
                return

            time.sleep(2)  # 给足够的时间让设置生效

            result = subprocess.run([betterdisplay_path, 'set', '-name=remote', '-hidpi=on'],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️ 设置远程显示器 HiDPI 失败: {result.stderr}")
            else:
                print("✅ betterdisplay remote 屏幕已启用")
        else:
            # 禁用远程显示器
            result = subprocess.run([betterdisplay_path, 'set', '-name=remote', '-connected=off'],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️ 禁用远程显示器失败: {result.stderr}")
            else:
                print("✅ betterdisplay remote 屏幕已禁用")
    except Exception as e:
        print(f"❌ 远程显示器控制失败: {e}")

def restore_main_display():
    """恢复主显示屏设置"""
    try:
        betterdisplay_path = '/Applications/BetterDisplay.app/Contents/MacOS/BetterDisplay'
        if not os.path.exists(betterdisplay_path):
            print("❌ BetterDisplay 应用未找到")
            return

        # 启用 screen 显示器
        result = subprocess.run([betterdisplay_path, 'set', '-name=screen', '-connected=on'],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"⚠️ 启用主显示器失败: {result.stderr}")
            return
        time.sleep(2)  # 给足够的时间让设置生效

        # 设置 HiDPI
        subprocess.run([betterdisplay_path, 'set', '-name=screen', '-hidpi=on'],
                      capture_output=True, text=True)
        time.sleep(2)  # 给足够的时间让设置生效

        # 设置旋转角度为 0
        subprocess.run([betterdisplay_path, 'set', '-name=screen', '-rotation=0'],
                      capture_output=True, text=True)
        time.sleep(2)  # 给足够的时间让设置生效

        # 设置 screen 为主显示屏
        result = subprocess.run([betterdisplay_path, 'set', '-name=screen', '-main=on'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 主显示屏设置已恢复")
        else:
            print(f"⚠️ 设置主显示屏失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 恢复主显示屏设置失败: {e}")

def control_nomachine_service(start=True):
    """控制 NoMachine 服务的启动和停止"""
    try:
        nxserver_path = '/Applications/NoMachine.app/Contents/Frameworks/bin/nxserver.app/Contents/MacOS/nxserver'

        # 检查 NoMachine 是否存在
        if not os.path.exists(nxserver_path):
            print("❌ NoMachine 应用未找到")
            return

        # 首先尝试使用 sudo（如果已配置无密码）
        try:
            if start:
                result = subprocess.run(['sudo', nxserver_path, '--startup'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ NoMachine 服务已启动")
                    return
            else:
                result = subprocess.run(['sudo', nxserver_path, '--shutdown'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ NoMachine 服务已停止")
                    return
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            print("⚠️ sudo 方式失败，尝试使用 osascript...")

        # 如果 sudo 失败，回退到 osascript 方式
        if start:
            cmd = f'do shell script "{nxserver_path} --startup" with administrator privileges'
            subprocess.run(['osascript', '-e', cmd], check=True)
            print("✅ NoMachine 服务已启动 (通过 osascript)")
        else:
            cmd = f'do shell script "{nxserver_path} --shutdown" with administrator privileges'
            subprocess.run(['osascript', '-e', cmd], check=True)
            print("✅ NoMachine 服务已停止 (通过 osascript)")

    except subprocess.CalledProcessError as e:
        print(f"❌ NoMachine 服务控制失败: {e}")
        print("⚠️ 继续执行其他操作...")
    except Exception as e:
        print(f"❌ NoMachine 服务控制出现未预期错误: {e}")
        print("⚠️ 继续执行其他操作...")

def startup_software():
    """启动软件"""
    apps = [
        '/Volumes/ybtan_nas/Applications/DingTalk.app'
    ]

    for app in apps:
        try:
            # 检查应用是否存在
            if not os.path.exists(app):
                print(f"⚠️ 应用不存在: {os.path.basename(app)}")
                continue

            # 使用 -j 参数强制在后台启动应用
            result = subprocess.run(['open', '-j', app], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ 正在启动: {os.path.basename(app)}")
            else:
                print(f"⚠️ 启动 {os.path.basename(app)} 失败: {result.stderr}")
            time.sleep(2)  # 给每个应用一些启动时间
        except Exception as e:
            print(f"❌ 启动 {os.path.basename(app)} 失败: {str(e)}")