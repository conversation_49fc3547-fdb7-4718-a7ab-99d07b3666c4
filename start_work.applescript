-- AppleScript for running start_work.py with proper permissions
-- 用于在快捷指令中执行的 AppleScript

try
    -- 设置脚本路径
    set scriptPath to "/Users/<USER>/Downloads/shell"
    
    -- 执行 Python 脚本
    set shellCommand to "cd " & quoted form of scriptPath & " && python3 start_work.py"
    
    -- 使用 do shell script 执行命令
    set result to do shell script shellCommand
    
    -- 显示结果
    display notification "工作环境启动完成" with title "Start Work"
    
    return result
    
on error errorMessage
    -- 错误处理
    display notification "启动失败: " & errorMessage with title "Start Work Error"
    return "Error: " & errorMessage
end try
