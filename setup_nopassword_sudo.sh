#!/bin/bash

# 设置 NoMachine 命令无需密码执行的脚本

echo "正在配置 NoMachine 命令的无密码 sudo 权限..."

# 获取当前用户名
USERNAME=$(whoami)

# NoMachine 服务器路径
NXSERVER_PATH="/Applications/NoMachine.app/Contents/Frameworks/bin/nxserver.app/Contents/MacOS/nxserver"

# 创建 sudoers 配置文件
SUDOERS_FILE="/etc/sudoers.d/nomachine"

# 检查 NoMachine 是否存在
if [ ! -f "$NXSERVER_PATH" ]; then
    echo "❌ NoMachine 未找到: $NXSERVER_PATH"
    exit 1
fi

echo "当前用户: $USERNAME"
echo "NoMachine 路径: $NXSERVER_PATH"

# 创建 sudoers 规则
SUDOERS_RULE="$USERNAME ALL=(ALL) NOPASSWD: $NXSERVER_PATH"

echo ""
echo "将要添加的 sudoers 规则:"
echo "$SUDOERS_RULE"
echo ""

# 提示用户确认
read -p "是否继续? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 1
fi

# 创建 sudoers 文件（需要管理员权限）
echo "正在创建 sudoers 配置文件..."
echo "$SUDOERS_RULE" | sudo tee "$SUDOERS_FILE" > /dev/null

if [ $? -eq 0 ]; then
    echo "✅ sudoers 配置已创建: $SUDOERS_FILE"
    
    # 验证配置
    echo "正在验证配置..."
    sudo visudo -c -f "$SUDOERS_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ sudoers 配置验证成功"
        echo ""
        echo "现在您可以无需密码执行以下命令:"
        echo "  sudo $NXSERVER_PATH --startup"
        echo "  sudo $NXSERVER_PATH --shutdown"
        echo ""
        echo "测试命令:"
        echo "  sudo $NXSERVER_PATH --status"
    else
        echo "❌ sudoers 配置验证失败，正在删除配置文件"
        sudo rm -f "$SUDOERS_FILE"
        exit 1
    fi
else
    echo "❌ 创建 sudoers 配置失败"
    exit 1
fi

echo ""
echo "🎉 配置完成！现在 NoMachine 命令可以无需密码执行了。"
