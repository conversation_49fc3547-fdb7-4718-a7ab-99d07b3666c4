#!/bin/bash

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 设置日志文件
LOG_FILE="$SCRIPT_DIR/start_work.log"

# 记录开始时间
echo "$(date): 开始执行 start_work 脚本" >> "$LOG_FILE"

# 执行 Python 脚本并记录输出
python3 start_work.py 2>&1 | tee -a "$LOG_FILE"

# 记录结束时间
echo "$(date): start_work 脚本执行完成" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"
