#!/usr/bin/env python3
"""
权限设置和检查脚本
用于解决语音命令执行时的权限问题
"""

import subprocess
import sys
import os

def check_accessibility_permissions():
    """检查辅助功能权限"""
    try:
        # 检查当前应用是否有辅助功能权限
        result = subprocess.run([
            'osascript', '-e', 
            'tell application "System Events" to get name of every process'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 辅助功能权限正常")
            return True
        else:
            print("❌ 缺少辅助功能权限")
            return False
    except Exception as e:
        print(f"❌ 检查辅助功能权限失败: {e}")
        return False

def setup_sudoers_for_nomachine():
    """设置 sudoers 以允许无密码执行 NoMachine 命令"""
    nxserver_path = '/Applications/NoMachine.app/Contents/Frameworks/bin/nxserver.app/Contents/MacOS/nxserver'
    
    # 获取当前用户
    username = os.getenv('USER')
    
    sudoers_line = f"{username} ALL=(ALL) NOPASSWD: {nxserver_path}"
    
    print("为了解决 NoMachine 权限问题，需要添加以下行到 /etc/sudoers:")
    print(f"  {sudoers_line}")
    print("\n请手动执行以下命令:")
    print(f"  sudo visudo")
    print(f"  然后添加: {sudoers_line}")
    print("\n或者运行:")
    print(f'  echo "{sudoers_line}" | sudo tee -a /etc/sudoers.d/nomachine')

def create_launch_agent():
    """创建 LaunchAgent 来处理权限问题"""
    plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.user.startwork</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/python3</string>
        <string>{os.path.abspath('start_work.py')}</string>
    </array>
    <key>RunAtLoad</key>
    <false/>
    <key>KeepAlive</key>
    <false/>
</dict>
</plist>"""

    launch_agents_dir = os.path.expanduser("~/Library/LaunchAgents")
    plist_path = os.path.join(launch_agents_dir, "com.user.startwork.plist")
    
    try:
        os.makedirs(launch_agents_dir, exist_ok=True)
        with open(plist_path, 'w') as f:
            f.write(plist_content)
        
        print(f"✅ LaunchAgent 已创建: {plist_path}")
        print("现在可以使用以下命令启动:")
        print("  launchctl load ~/Library/LaunchAgents/com.user.startwork.plist")
        print("  launchctl start com.user.startwork")
        
        return plist_path
    except Exception as e:
        print(f"❌ 创建 LaunchAgent 失败: {e}")
        return None

def show_shortcut_instructions():
    """显示快捷指令配置说明"""
    print("\n" + "="*60)
    print("快捷指令配置建议:")
    print("="*60)
    print("1. 在快捷指令中，不要直接运行 Python 脚本")
    print("2. 而是使用以下方法之一:")
    print("\n方法一: 使用 LaunchAgent")
    print("  - 运行命令: launchctl start com.user.startwork")
    print("\n方法二: 使用 osascript")
    print("  - 在快捷指令中添加 '运行 AppleScript' 操作")
    print("  - 使用以下 AppleScript 代码:")
    print('    do shell script "cd /Users/<USER>/Downloads/shell && python3 start_work.py"')
    print("\n方法三: 使用 shell 脚本")
    print("  - 创建一个 .sh 脚本文件")
    print("  - 在快捷指令中运行该脚本")

def main():
    print("开始权限检查和设置...")
    
    # 检查辅助功能权限
    check_accessibility_permissions()
    
    # 设置 sudoers
    setup_sudoers_for_nomachine()
    
    # 创建 LaunchAgent
    create_launch_agent()
    
    # 显示快捷指令配置说明
    show_shortcut_instructions()

if __name__ == "__main__":
    main()
