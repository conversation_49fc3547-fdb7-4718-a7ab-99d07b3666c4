#!/usr/bin/python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import argparse
import re
import time

def find_app_process(app_name, external_drive_path=None):
    """
    查找指定应用名称的进程
    
    Args:
        app_name: 应用名称
        external_drive_path: 外挂硬盘路径，如果为None则搜索所有位置
    
    Returns:
        包含进程ID的列表
    """
    try:
        # 处理应用名称，移除.app后缀（如果存在）
        base_app_name = app_name.replace('.app', '')
        
        # 获取当前脚本的PID
        current_pid = str(os.getpid())
        
        # 使用ps命令获取所有进程信息，添加更多详细信息
        result = subprocess.run(['ps', '-ax', '-o', 'pid,command'], capture_output=True, text=True)
        processes = result.stdout.split('\n')
        
        # 过滤出包含应用名称的进程
        matching_processes = []
        for process in processes:
            # 跳过空行和标题行
            if not process.strip() or 'PID' in process:
                continue
                
            # 提取进程ID和完整命令
            match = re.search(r'^\s*(\d+)\s+(.+)$', process)
            if match:
                pid = match.group(1)
                command = match.group(2)
                
                # 跳过当前脚本的进程
                if pid == current_pid:
                    continue
                
                # 检查进程是否包含应用名称（不区分大小写）
                if base_app_name.lower() in command.lower():
                    # 如果指定了外挂硬盘路径，检查进程是否在该路径下
                    if external_drive_path:
                        if external_drive_path in command:
                            matching_processes.append(pid)
                    else:
                        matching_processes.append(pid)
        
        return matching_processes
    except Exception as e:
        print(f"查找进程时出错: {e}")
        return []

def kill_process(pid):
    """
    终止指定PID的进程
    
    Args:
        pid: 进程ID
    
    Returns:
        是否成功终止进程
    """
    try:
        print(f"正在终止进程 {pid}...")
        result = subprocess.run(['kill', pid], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"成功终止进程 {pid}")
            return True
        else:
            print(f"终止进程 {pid} 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"终止进程 {pid} 时出错: {e}")
        return False

def force_kill_process(pid):
    """
    强制终止指定PID的进程
    
    Args:
        pid: 进程ID
    
    Returns:
        是否成功终止进程
    """
    try:
        print(f"正在强制终止进程 {pid}...")
        result = subprocess.run(['kill', '-9', pid], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"成功强制终止进程 {pid}")
            return True
        else:
            print(f"强制终止进程 {pid} 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"强制终止进程 {pid} 时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='在Mac系统下退出指定应用程序')
    parser.add_argument('app_names', help='要退出的应用名称，多个应用用逗号分隔')
    parser.add_argument('-d', '--drive', help='外挂硬盘路径')
    parser.add_argument('-f', '--force', action='store_true', help='强制终止进程')
    
    args = parser.parse_args()
    
    # 分割应用名称
    app_list = [app.strip() for app in args.app_names.split(',')]
    
    all_pids = set()  # 使用集合来避免重复的PID
    for app_name in app_list:
        # 查找进程
        pids = find_app_process(app_name, args.drive)
        
        if not pids:
            print(f"未找到应用 '{app_name}' 的进程")
            if args.drive:
                print(f"在路径 '{args.drive}' 下")
            continue
        
        print(f"找到应用 '{app_name}' 的 {len(pids)} 个匹配进程:")
        for pid in pids:
            print(f"  PID: {pid}")
            all_pids.add(pid)
    
    if not all_pids:
        print("未找到任何匹配的进程")
        return
    
    # 终止进程
    success_count = 0
    failed_pids = []
    
    for pid in all_pids:
        try:
            if args.force:
                if force_kill_process(pid):
                    success_count += 1
                    # 添加短暂延迟，避免过快终止进程
                    time.sleep(0.1)
                else:
                    failed_pids.append(pid)
            else:
                if kill_process(pid):
                    success_count += 1
                    # 添加短暂延迟，避免过快终止进程
                    time.sleep(0.1)
                else:
                    failed_pids.append(pid)
        except Exception as e:
            print(f"处理进程 {pid} 时出错: {e}")
            failed_pids.append(pid)
            continue
    
    print(f"\n总结:")
    print(f"成功终止 {success_count}/{len(all_pids)} 个进程")
    if failed_pids:
        print(f"以下进程终止失败: {', '.join(failed_pids)}")

if __name__ == "__main__":
    main()
