#!/usr/bin/env python3
import sys
import os
import subprocess
import logging
from datetime import datetime
from display_utils import control_remote_display, restore_main_display
from fileorg import organize_files

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('end_work.log'),
        logging.StreamHandler()
    ]
)

def disable_remote_desktop():
    """禁用远程桌面（无管理员权限版本）"""
    try:
        logging.info("开始禁用远程桌面设置")

        # 跳过 NoMachine 服务控制（避免管理员权限要求）
        logging.info("跳过 NoMachine 服务控制（避免管理员权限要求）")

        # 启用远程显示器
        logging.info("启用远程显示器")
        control_remote_display(enable=True)

        # 恢复主显示屏设置
        logging.info("恢复主显示屏设置")
        restore_main_display()

        logging.info("远程桌面设置完成")
        print("✅ 远程桌面设置完成（跳过 NoMachine 服务控制）")

    except Exception as e:
        logging.error(f"远程桌面设置失败: {e}")
        print(f"❌ 远程桌面设置失败: {e}")
        # 不要退出，继续执行其他操作
        print("⚠️ 继续执行其他操作...")

def organize_desktop_files():
    """整理桌面文件"""
    try:
        logging.info("开始整理桌面文件")
        target_directory = '/Volumes/ybtan_nas/资料'

        if not os.path.exists(target_directory):
            logging.warning(f"目标目录不存在: {target_directory}")
            print(f"⚠️ 目录 {target_directory} 不存在，跳过文件整理")
            return False
        else:
            organize_files(target_directory)
            logging.info("文件整理完成")
            print("✅ 文件整理完成！")
            return True
    except Exception as e:
        logging.error(f"文件整理失败: {e}")
        print(f"❌ 文件整理失败: {e}")
        return False

def kill_apps():
    """关闭指定应用程序"""
    try:
        logging.info("开始关闭应用程序")
        app_names = "京办,读写客,BaiduNetdisk,ChatGPT,Cherry,DingTalk,Find,LocalSend,OmniPlayerStore,QQ,QQLive,splayer,scrcpy,TencentMeeting"
        drive_path = "/Volumes/ybtan_nas/Applications"

        # 检查 killapp.py 是否存在
        killapp_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'killapp.py')
        if not os.path.exists(killapp_path):
            logging.warning(f"killapp.py 不存在: {killapp_path}")
            print("⚠️ killapp.py 不存在，跳过应用程序关闭")
            return False

        # 使用 killapp.py 关闭应用
        result = subprocess.run([
            'python3',
            killapp_path,
            app_names,
            '-d', drive_path
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            logging.info("应用程序关闭成功")
            print("✅ 应用程序已关闭")
            if result.stdout.strip():
                print(result.stdout)
            return True
        else:
            logging.error(f"关闭应用程序失败: {result.stderr}")
            print(f"❌ 关闭应用程序失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logging.error("关闭应用程序超时")
        print("❌ 关闭应用程序超时")
        return False
    except Exception as e:
        logging.error(f"关闭应用程序失败: {e}")
        print(f"❌ 关闭应用程序失败: {e}")
        return False

def lock_screen():
    """锁定屏幕"""
    try:
        logging.info("开始锁定屏幕")
        # 使用 macOS 的命令锁定屏幕
        result = subprocess.run(['open', '-a', '/System/Library/CoreServices/ScreenSaverEngine.app'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            logging.info("屏幕锁定成功")
            print("✅ 屏幕已锁定")
            return True
        else:
            logging.error(f"屏幕锁定失败: {result.stderr}")
            print(f"❌ 屏幕锁定失败: {result.stderr}")
            return False
    except Exception as e:
        logging.error(f"锁定屏幕失败: {e}")
        print(f"❌ 锁定屏幕失败: {e}")
        return False

def set_screen_as_main_display():
    """设置 screen 为主显示屏"""
    try:
        logging.info("开始设置 screen 为主显示屏")
        # 使用 restore_main_display 函数设置 screen 为主显示屏
        restore_main_display()
        logging.info("screen 设置为主显示屏成功")
        print("✅ screen 已设置为主显示屏")
        return True
    except Exception as e:
        logging.error(f"设置 screen 为主显示屏失败: {e}")
        print(f"❌ 设置 screen 为主显示屏失败: {e}")
        return False

def end_work():
    """结束工作流程（无管理员权限版本）"""
    logging.info("开始执行结束工作流程（无管理员权限版本）")
    print("=== 开始执行结束工作流程 ===")

    success_count = 0
    total_operations = 5

    try:
        # 步骤1: 整理桌面文件
        logging.info("步骤 1/5: 整理桌面文件")
        try:
            if organize_desktop_files():
                success_count += 1
        except Exception as e:
            logging.error(f"整理桌面文件失败: {e}")

        # 步骤2: 关闭应用程序
        logging.info("步骤 2/5: 关闭应用程序")
        try:
            if kill_apps():
                success_count += 1
        except Exception as e:
            logging.error(f"关闭应用程序失败: {e}")

        # 步骤3: 设置 screen 为主显示屏
        logging.info("步骤 3/5: 设置 screen 为主显示屏")
        try:
            if set_screen_as_main_display():
                success_count += 1
        except Exception as e:
            logging.error(f"设置主显示屏失败: {e}")

        # 步骤4: 配置远程桌面
        logging.info("步骤 4/5: 配置远程桌面")
        try:
            disable_remote_desktop()
            success_count += 1
        except Exception as e:
            logging.error(f"配置远程桌面失败: {e}")

        # 步骤5: 锁定屏幕
        logging.info("步骤 5/5: 锁定屏幕")
        try:
            if lock_screen():
                success_count += 1
        except Exception as e:
            logging.error(f"锁定屏幕失败: {e}")

        # 总结
        logging.info(f"结束工作流程完成: {success_count}/{total_operations} 个操作成功")

        if success_count == total_operations:
            print("🎉 所有操作都成功完成!")
            print("ℹ️ 注意: NoMachine 服务控制已跳过（避免权限问题）")
        elif success_count > 0:
            print(f"⚠️ 部分操作完成 ({success_count}/{total_operations})")
        else:
            print("❌ 所有操作都失败了")

        print("=== 结束工作流程执行完毕 ===")

    except Exception as e:
        logging.error(f"结束工作流程执行过程中发生未预期的错误: {e}")
        print(f"❌ 结束工作流程执行失败: {e}")

if __name__ == "__main__":
    end_work()