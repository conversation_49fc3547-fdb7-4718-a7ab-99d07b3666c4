#!/usr/bin/env python3
from display_utils import control_remote_display, control_nomachine_service, restore_main_display
import sys
import os
import subprocess
from fileorg import organize_files

def disable_remote_desktop():
    """禁用远程桌面"""
    try:
        # 启动 nomachine 服务
        control_nomachine_service(start=True)

        # 启用远程显示器
        control_remote_display(enable=True)

        # 禁用 screen 显示器
        #control_screen_display(enable=False)

        # 禁用 mainsc 显示器
        #control_mainsc_display()
        restore_main_display()

    except Exception as e:
        print(f"❌ 远程桌面禁用失败: {e}")
        sys.exit(1)

def organize_desktop_files():
    """整理桌面文件"""
    try:
        print("开始整理桌面文件...")
        target_directory = '/Volumes/ybtan_nas/资料'

        if not os.path.exists(target_directory):
            print(f"目录 {target_directory} 不存在！")
            return False
        else:
            organize_files(target_directory)
            print("✅ 文件整理完成！")
            return True
    except Exception as e:
        print(f"❌ 文件整理失败: {e}")
        return False

def kill_apps():
    """关闭指定应用程序"""
    try:
        print("开始关闭应用程序...")
        app_names = "京办,读写客,BaiduNetdisk,ChatGPT,Cherry,DingTalk,Find,LocalSend,OmniPlayerStore,QQ,QQLive,splayer,scrcpy,TencentMeeting"
        drive_path = "/Volumes/ybtan_nas/Applications"

        # 使用 killapp.py 关闭应用
        result = subprocess.run([
            'python3',
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'killapp.py'),
            app_names,
            '-d', drive_path
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 应用程序已关闭")
            print(result.stdout)
            return True
        else:
            print(f"❌ 关闭应用程序失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 关闭应用程序失败: {e}")
        return False

def lock_screen():
    """锁定屏幕"""
    try:
        print("正在锁定屏幕...")
        # 使用 macOS 的命令锁定屏幕 (loginwindow的方法)
        subprocess.run(['open', '-a', '/System/Library/CoreServices/ScreenSaverEngine.app'])
        print("✅ 屏幕已锁定")
        return True
    except Exception as e:
        print(f"❌ 锁定屏幕失败: {e}")
        return False

def set_screen_as_main_display():
    """设置 screen 为主显示屏"""
    try:
        print("正在设置 screen 为主显示屏...")

        # 使用 restore_main_display 函数设置 screen 为主显示屏
        restore_main_display()

        print("✅ screen 已设置为主显示屏")
        return True
    except Exception as e:
        print(f"❌ 设置 screen 为主显示屏失败: {e}")
        return False

def end_work():
    """结束工作流程"""
    print("=== 开始执行结束工作流程 ===")

    # 步骤1: 整理桌面文件
    organize_desktop_files()

    # 步骤2: 关闭应用程序
    kill_apps()

    # 步骤4: 设置 screen 为主显示屏
    set_screen_as_main_display()

    # 步骤5: 禁用远程桌面
    disable_remote_desktop()

    # 步骤6: 锁定屏幕
    lock_screen()

    print("=== 结束工作流程执行完毕 ===")

if __name__ == "__main__":
    end_work()