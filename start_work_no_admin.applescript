-- AppleScript for running start_work_no_admin.py (无需管理员权限版本)
-- 用于在快捷指令中执行的 AppleScript

try
    -- 设置脚本路径
    set scriptPath to "/Users/<USER>/Downloads/shell"
    
    -- 执行无管理员权限版本的 Python 脚本
    set shellCommand to "cd " & quoted form of scriptPath & " && python3 start_work_no_admin.py"
    
    -- 使用 do shell script 执行命令
    set result to do shell script shellCommand
    
    -- 显示成功通知
    display notification "工作环境启动完成（无需密码）" with title "Start Work - No Admin"
    
    return result
    
on error errorMessage
    -- 错误处理
    display notification "启动失败: " & errorMessage with title "Start Work Error"
    return "Error: " & errorMessage
end try
