#!/usr/bin/env python3
import sys
import os
from display_utils import control_remote_display, startup_software, control_nomachine_service, restore_main_display

def enable_remote_desktop():
    """启用远程桌面"""
    try:
        # 禁用远程显示器
        control_remote_display(enable=False)

        restore_main_display()
        
        # 停止 nomachine 服务
        control_nomachine_service(start=False)

        # 启动软件
        startup_software()
    except Exception as e:
        print(f"❌ 远程桌面启用失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    enable_remote_desktop() 