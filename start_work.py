#!/usr/bin/env python3
import sys
import os
import logging
from datetime import datetime
from display_utils import control_remote_display, startup_software, control_nomachine_service, restore_main_display

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('start_work.log'),
        logging.StreamHandler()
    ]
)

def enable_remote_desktop():
    """启用远程桌面"""
    logging.info("开始执行工作环境启动脚本")

    success_count = 0
    total_operations = 4

    try:
        # 1. 禁用远程显示器
        logging.info("步骤 1/4: 禁用远程显示器")
        try:
            control_remote_display(enable=False)
            success_count += 1
        except Exception as e:
            logging.error(f"禁用远程显示器失败: {e}")

        # 2. 恢复主显示屏
        logging.info("步骤 2/4: 恢复主显示屏设置")
        try:
            restore_main_display()
            success_count += 1
        except Exception as e:
            logging.error(f"恢复主显示屏失败: {e}")

        # 3. 停止 nomachine 服务
        logging.info("步骤 3/4: 停止 NoMachine 服务")
        try:
            control_nomachine_service(start=False)
            success_count += 1
        except Exception as e:
            logging.error(f"停止 NoMachine 服务失败: {e}")

        # 4. 启动软件
        logging.info("步骤 4/4: 启动应用程序")
        try:
            startup_software()
            success_count += 1
        except Exception as e:
            logging.error(f"启动软件失败: {e}")

        # 总结
        logging.info(f"脚本执行完成: {success_count}/{total_operations} 个操作成功")

        if success_count == total_operations:
            print("🎉 所有操作都成功完成!")
        elif success_count > 0:
            print(f"⚠️ 部分操作完成 ({success_count}/{total_operations})")
        else:
            print("❌ 所有操作都失败了")
            sys.exit(1)

    except Exception as e:
        logging.error(f"脚本执行过程中发生未预期的错误: {e}")
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    enable_remote_desktop()