# 语音命令执行权限问题解决方案

## 问题描述

Python 脚本 `start_work.py` 在以下情况下的执行结果：
- ✅ Mac shell 下直接执行：正常
- ✅ 快捷指令手动执行：正常  
- ❌ 语音命令控制快捷指令执行：出现 "operation not permitted" 错误

## 问题原因

1. **权限上下文不同**：语音助手执行时的权限级别比手动执行更受限
2. **sudo 命令无法交互**：语音执行时无法输入密码
3. **辅助功能权限**：BetterDisplay 需要辅助功能权限，语音执行时可能被拒绝

## 解决方案

### 方案一：使用 AppleScript（推荐）

1. 在快捷指令中删除原来的 "运行脚本" 操作
2. 添加 "运行 AppleScript" 操作
3. 使用以下 AppleScript 代码：

```applescript
try
    set scriptPath to "/Users/<USER>/Downloads/shell"
    set shellCommand to "cd " & quoted form of scriptPath & " && python3 start_work.py"
    set result to do shell script shellCommand
    display notification "工作环境启动完成" with title "Start Work"
    return result
on error errorMessage
    display notification "启动失败: " & errorMessage with title "Start Work Error"
    return "Error: " & errorMessage
end try
```

### 方案二：使用 Shell 脚本

1. 使用提供的 `start_work.sh` 脚本
2. 在快捷指令中运行：`/Users/<USER>/Downloads/shell/start_work.sh`

### 方案三：权限配置（高级用户）

1. 运行权限设置脚本：
   ```bash
   python3 setup_permissions.py
   ```

2. 按照提示配置 sudoers 文件以允许无密码执行 NoMachine 命令

## 已做的改进

### 1. 错误处理改进
- 使用 `osascript` 替代 `sudo` 命令，避免密码输入问题
- 添加详细的错误捕获和日志记录
- 每个操作都有独立的错误处理，失败不会影响其他操作

### 2. 权限检查
- 检查应用程序是否存在再执行操作
- 使用 `capture_output=True` 捕获错误信息
- 提供更友好的错误提示

### 3. 日志记录
- 添加详细的日志记录到 `start_work.log` 文件
- 记录每个步骤的执行状态
- 便于问题排查

## 文件说明

- `start_work.py` - 改进后的主脚本，增强错误处理
- `display_utils.py` - 改进后的工具函数，解决权限问题
- `start_work.sh` - Shell 脚本包装器
- `start_work.applescript` - AppleScript 版本
- `setup_permissions.py` - 权限配置脚本
- `start_work.log` - 执行日志文件

## 测试建议

1. 先在终端测试改进后的脚本：
   ```bash
   python3 start_work.py
   ```

2. 测试 Shell 脚本：
   ```bash
   ./start_work.sh
   ```

3. 在快捷指令中测试 AppleScript 方案

4. 最后测试语音命令执行

## 注意事项

1. **辅助功能权限**：确保快捷指令应用有辅助功能权限
2. **文件路径**：确保所有路径都是绝对路径
3. **应用程序权限**：BetterDisplay 和 NoMachine 需要相应的系统权限
4. **网络存储**：DingTalk 应用在网络存储上，确保网络连接正常

## 故障排除

如果仍然遇到问题：

1. 查看 `start_work.log` 文件了解详细错误信息
2. 检查系统偏好设置中的隐私和安全性设置
3. 确保快捷指令应用有必要的权限
4. 尝试重启快捷指令应用或重新创建快捷指令
